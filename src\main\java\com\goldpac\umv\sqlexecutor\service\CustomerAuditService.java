package com.goldpac.umv.sqlexecutor.service;

import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.goldpac.umv.framework.pojo.CommonResult;
import com.goldpac.umv.sqlexecutor.framework.dbquery.util.PaginationUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户审计服务
 * 用于处理复杂的客户审计记录变更逻辑
 */
@Slf4j
@Service
public class CustomerAuditService {

    @Autowired
    private SqlSession sqlSession;

    /**
     * 获取客户审计记录变更
     *
     * @param params 查询参数
     * @return 变更记录
     */
    public CommonResult<Object> getCustomerAuditRecordChanges(Map<String, Object> params) {
        try {
            String tenantId = (String) params.get("tenantId");
            String customerId = (String) params.get("customerId");
            String code = (String) params.get("code");
            Object timeObj = params.get("time");
            Object pageNumObj = params.get("pageNum");
            Object pageSizeObj = params.get("pageSize");

            if (tenantId == null || tenantId.trim().isEmpty()) {
                return CommonResult.error(400, "tenantId 参数不能为空");
            }

            // 1. 获取所有最新套餐
            List<Map<String, Object>> allPackages = sqlSession.selectList("manifest.getAllNewPackage");

            // 2. 获取租户当前订阅的套餐和服务
            Map<String, Object> tenantParams = new HashMap<>();
            tenantParams.put("tenantId", tenantId);
            List<Map<String, Object>> currentSubscriptions = sqlSession.selectList(
                    "manifest.getAllNewPackageAndServiceByTenantId", tenantParams);

            // 3. 处理数据并生成变更记录（按Node.js版本的逻辑）
            List<Map<String, Object>> timeList = processAuditChangesLikeNode(allPackages, currentSubscriptions,
                    tenantId);

            // 4. 应用过滤条件
            List<Map<String, Object>> filteredList = applyFilters(timeList, code, timeObj);

            // 5. 应用分页
            Map<String, Object> paginatedResult = applyPagination(filteredList, pageNumObj, pageSizeObj);

            return CommonResult.success(paginatedResult);

        } catch (Exception e) {
            log.error("处理客户审计记录变更时发生错误", e);
            return CommonResult.error(500, "处理客户审计记录变更时发生错误: " + e.getMessage());
        }
    }

    /**
     * 按照Node.js版本的逻辑处理审计变更记录
     */
    private List<Map<String, Object>> processAuditChangesLikeNode(
            List<Map<String, Object>> allPackages,
            List<Map<String, Object>> currentSubscriptions,
            String tenantId) {

        // 创建套餐代码到套餐信息的映射
        Map<String, String> allNewPackageNameMap = new HashMap<>();
        List<Map<String, Object>> allNewPackageList = new ArrayList<>();

        // 处理所有套餐数据
        for (Map<String, Object> pkg : allPackages) {
            Map<String, Object> packageItem = new HashMap<>();
            packageItem.put("package_name", pkg.get("name"));
            packageItem.put("package_code", pkg.get("code"));
            packageItem.put("package_id", pkg.get("id"));
            packageItem.put("classes", "1");
            allNewPackageList.add(packageItem);
            allNewPackageNameMap.put((String) pkg.get("code"), (String) pkg.get("name"));
        }

        // 按时间分组处理订阅记录
        Map<String, List<Map<String, Object>>> timeMap = new HashMap<>();

        for (Map<String, Object> subscription : currentSubscriptions) {
            String createTime = (String) subscription.get("create_time");
            String createDay = formatDateToDay(createTime);

            Map<String, Object> subItem = new HashMap<>(subscription);
            subItem.put("create_day", createDay);

            timeMap.computeIfAbsent(createDay, k -> new ArrayList<>()).add(subItem);
        }

        // 合并定制服务到套餐列表中
        allNewPackageList = mergeAndDeduplicate(allNewPackageList, currentSubscriptions, "package_code");

        // 生成时间线列表
        List<Map<String, Object>> timeList = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : timeMap.entrySet()) {
            Map<String, Object> timeItem = new HashMap<>();
            timeItem.put("time", entry.getKey());
            timeItem.put("list", entry.getValue());
            timeItem.put("unHandledList", entry.getValue());
            timeList.add(timeItem);
        }

        // 按时间排序（最新的在前）
        timeList.sort((a, b) -> {
            String timeA = (String) a.get("time");
            String timeB = (String) b.get("time");
            return timeB.compareTo(timeA);
        });

        // 处理去重和排序逻辑
        for (int i = timeList.size() - 2; i >= 0; i--) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> nowRecord = (List<Map<String, Object>>) timeList.get(i).get("list");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> preRecord = (List<Map<String, Object>>) timeList.get(i + 1).get("list");

            List<Map<String, Object>> mergedList = mergeAndDeduplicate(nowRecord, preRecord, "package_code");
            timeList.get(i).put("list", mergedList);
        }

        // 处理每个时间点的套餐列表
        for (Map<String, Object> timeItem : timeList) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> originList = (List<Map<String, Object>>) timeItem.get("list");
            timeItem.put("originList", new ArrayList<>(originList));

            // 提取package_code列表
            List<String> codeList = originList.stream()
                    .map(item -> (String) item.get("package_code"))
                    .collect(Collectors.toList());
            timeItem.put("list", codeList);
        }

        // 获取第一个时间点的代码列表用于排序
        List<String> preSortSubCodeList = timeList.isEmpty() ? new ArrayList<>()
                : (List<String>) timeList.get(0).get("list");

        // 对所有套餐进行排序（已订阅的在前面）
        allNewPackageList.sort((a, b) -> {
            String codeA = (String) a.get("package_code");
            String codeB = (String) b.get("package_code");
            int sortA = preSortSubCodeList.indexOf(codeA);
            int sortB = preSortSubCodeList.indexOf(codeB);

            if (sortA == sortB)
                return 0;
            if (sortA == -1)
                return 1;
            if (sortB == -1)
                return -1;
            return sortA - sortB;
        });

        // 为每个时间点生成完整的套餐列表
        for (Map<String, Object> timeItem : timeList) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> unHandledList = (List<Map<String, Object>>) timeItem.get("unHandledList");
            @SuppressWarnings("unchecked")
            List<String> subscribedCodes = (List<String>) timeItem.get("list");

            Map<String, Map<String, Object>> codeMap = unHandledList.stream()
                    .collect(Collectors.toMap(
                            item -> (String) item.get("package_code"),
                            item -> item,
                            (existing, replacement) -> existing));

            List<Map<String, Object>> allPackageList = new ArrayList<>();
            for (Map<String, Object> packageItem : allNewPackageList) {
                Map<String, Object> item = new HashMap<>(packageItem);
                String packageCode = (String) item.get("package_code");

                // 设置套餐名称
                if (allNewPackageNameMap.containsKey(packageCode)) {
                    item.put("package_name", allNewPackageNameMap.get(packageCode));
                }

                // 设置是否订阅
                item.put("isSubscribed", subscribedCodes.contains(packageCode));

                // 设置是否有变更
                if (codeMap.containsKey(packageCode)) {
                    item.put("id", codeMap.get(packageCode).get("id"));
                    item.put("isChange", true);
                } else {
                    item.put("isChange", false);
                }

                allPackageList.add(item);
            }

            timeItem.put("allPackageList", allPackageList);
        }

        return timeList;
    }

    /**
     * 格式化日期为天（YYYY-MM-DD格式）
     */
    private String formatDateToDay(String dateTime) {
        if (dateTime == null || dateTime.trim().isEmpty()) {
            return "";
        }
        try {
            // 假设输入格式为 "YYYY-MM-DD HH:mm:ss"
            if (dateTime.length() >= 10) {
                return dateTime.substring(0, 10);
            }
            return dateTime;
        } catch (Exception e) {
            return dateTime;
        }
    }

    /**
     * 合并并去重两个列表
     */
    private List<Map<String, Object>> mergeAndDeduplicate(
            List<Map<String, Object>> list1,
            List<Map<String, Object>> list2,
            String keyField) {

        Map<String, Map<String, Object>> map = new LinkedHashMap<>();

        // 先添加list2的元素
        for (Map<String, Object> item : list2) {
            String key = (String) item.get(keyField);
            if (key != null) {
                map.put(key, item);
            }
        }

        // 再添加list1的元素（如果key不存在的话）
        for (Map<String, Object> item : list1) {
            String key = (String) item.get(keyField);
            if (key != null && !map.containsKey(key)) {
                map.put(key, item);
            }
        }

        return new ArrayList<>(map.values());
    }

    /**
     * 应用过滤条件
     */
    private List<Map<String, Object>> applyFilters(
            List<Map<String, Object>> timeList,
            String code,
            Object timeObj) {

        List<Map<String, Object>> filteredList = new ArrayList<>(timeList);

        // 按id过滤
        if (code != null && !code.trim().isEmpty()) {
            filteredList = filteredList.stream()
                    .filter(timeItem -> {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> allPackageList = (List<Map<String, Object>>) timeItem
                                .get("allPackageList");



                        return allPackageList.stream()
                                .anyMatch(pkg -> code.equals(String.valueOf(pkg.get("package_code"))) &&
                                        Boolean.TRUE.equals(pkg.get("isSubscribed")));
                    })
                    .collect(Collectors.toList());
        }

        // 按时间范围过滤
        if (timeObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> timeRange = (List<String>) timeObj;
            if (timeRange.size() == 2) {
                String startTime = timeRange.get(0);
                String endTime = timeRange.get(1);

                filteredList = filteredList.stream()
                        .filter(timeItem -> {
                            String itemTime = (String) timeItem.get("time");
                            return itemTime.compareTo(startTime) >= 0 &&
                                    itemTime.compareTo(endTime) <= 0;
                        })
                        .collect(Collectors.toList());
            }
        }

        return filteredList;
    }

    /**
     * 应用分页
     */
    private Map<String, Object> applyPagination(
            List<Map<String, Object>> filteredList,
            Object pageNumObj,
            Object pageSizeObj) {

        // 去重处理（按日期）
        List<Map<String, Object>> uniqueList = filterDateArray(filteredList);

        // 使用 PaginationUtil 处理分页参数
        Map<String, Object> paginationParams = new HashMap<>();
        paginationParams.put("pageNum", pageNumObj);
        paginationParams.put("pageSize", pageSizeObj);
        PaginationUtil.processPaginationParams(paginationParams);

        int pageNum = (Integer) paginationParams.get("pageNumInt");
        int pageSize = (Integer) paginationParams.get("pageSizeInt");

        // 验证分页参数
        if (!PaginationUtil.isValidPagination(pageNumObj, pageSizeObj)) {
            pageNum = 1;
            pageSize = 20;
        }

        List<Map<String, Object>> paginatedList = paginateArray(uniqueList, pageNum, pageSize);

        Map<String, Object> result = new HashMap<>();
        result.put("list", paginatedList);
        result.put("total", uniqueList.size());

        return result;
    }

    /**
     * 过滤日期数组，去除重复日期
     */
    private List<Map<String, Object>> filterDateArray(List<Map<String, Object>> list) {
        Map<String, Map<String, Object>> dateMap = new LinkedHashMap<>();

        for (Map<String, Object> item : list) {
            String time = (String) item.get("time");
            if (time != null && !dateMap.containsKey(time)) {
                dateMap.put(time, item);
            }
        }

        return new ArrayList<>(dateMap.values());
    }

    /**
     * 数组分页
     */
    private List<Map<String, Object>> paginateArray(
            List<Map<String, Object>> array,
            int pageNum,
            int pageSize) {

        if (array == null || pageSize <= 0 || pageNum <= 0) {
            return new ArrayList<>();
        }

        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, array.size());

        if (startIndex >= array.size()) {
            return new ArrayList<>();
        }

        return new ArrayList<>(array.subList(startIndex, endIndex));
    }

}
