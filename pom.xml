<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.goldpac.umv</groupId>
    <artifactId>umv-sqlexecutor-backend</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.goldpac.umv</groupId>
                <artifactId>umv-dependencies</artifactId>
                <version>1.6.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>com.goldpac.umv</groupId>
            <artifactId>umv-springcloud-starter-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.goldpac.umv</groupId>
            <artifactId>umv-springcloud-starter-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>com.goldpac.cust.front</groupId>
            <artifactId>client-front</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <classifier>microfront-sqlexecutor-rest</classifier>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.6.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>