<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="manifest">
    <!--    获取套餐-->
    <select id="getPackage" resultType="java.util.Map">
        SELECT jt.date_val, MAX(bus.id) as id, MAX(bus.name) as name, MAX(bus.status) as status, MAX(bus.ver) as ver, MAX(bus.create_time) as create_time,  DATE_FORMAT(MAX(bus.update_time), '%Y-%m-%d %H:%i:%s') as update_time, MAX(bus.code) as code, 1 as classes, NULL AS application_owner FROM manifest m JOIN JSON_TABLE( JSON_EXTRACT(m.manifest, '$.created'), '$[*]' COLUMNS (date_val DECIMAL(16, 1) PATH '$') ) jt LEFT JOIN system_tenant_package bus ON bus.code = m.entity_id WHERE m.entity = 'system_tenant_package' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')
        <if test="status != null">
            AND bus.status = #{status}
        </if>
        <if test="name != null">
            <bind name="pattern" value="'%' + name + '%'"></bind>
            AND bus.name LIKE #{pattern}
        </if>
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.deleted = 0 GROUP BY bus.code ORDER BY create_time DESC
    </select>
    <!--    获取服务-->
    <select id="getService" resultType="java.util.Map">
        SELECT MAX(jt.date_val) as date_val, MAX(bus.id) as id ,MAX(bus.status) as status, MAX(bus.name) as name, MAX(bus.code) as code, MAX(bus.NAME) as name, MAX(bus.STATUS) as status, MAX(bus.ver) as ver, MAX(bus.create_time) as create_time, DATE_FORMAT(MAX(bus.update_time), '%Y-%m-%d %H:%i:%s') as update_time, 2 AS classes, MAX(bus.application_owner) as application_owner FROM manifest m JOIN JSON_TABLE(JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt  LEFT JOIN system_tenant_application bus ON bus.code = m.entity_id WHERE  m.entity = 'system_tenant_application' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')         <if test="status != null">
            AND bus.status = #{status}
        </if>
        <if test="name != null">
            <bind name="pattern" value="'%' + name + '%'"></bind>
            AND bus.name LIKE #{pattern}
        </if>
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND application_owner != '' AND bus.id IS NOT NULL AND bus.deleted = 0 GROUP BY bus.CODE
    </select>
    <!--    根据套餐获取订阅客户数量（所有）-->
    <select id="getPackageCustomerSubList" resultType="java.util.Map">
        SELECT  bus.package_code as pck_code, count( DISTINCT bus.tenant_id ) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val  DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN system_tenant_package_subscription bus ON bus.package_code = m.entity_id AND bus.tenant_id = m.entity_id_2nd WHERE m.entity = 'system_tenant_package_subscription' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')  AND bus.id IS NOT NULL
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.STATUS = 0 AND bus.deleted = 0 GROUP BY bus.package_code
    </select>
    <!--    根据服务获取订阅客户数量（所有）-->
    <select id="getServiceCustomerSubList" resultType="java.util.Map">
        SELECT app.`code`, COUNT(DISTINCT tenant_id) as tenantIdCount, GROUP_CONCAT(tenant_id) AS tenantIds  FROM system_tenant_application_subscription sub LEFT JOIN system_tenant_application app ON app.id = sub.application_id WHERE app.status = 0 AND app.deleted =0 AND sub.deleted=0 AND sub.status =0 and ( sub.cdc_op IS NULL OR sub.cdc_op != 'd') and ( app.cdc_op IS NULL OR app.cdc_op != 'd')
        <if test="date_val != null">
            AND sub.create_time &gt; #{date_val[0]} AND sub.create_time &lt; #{date_val[1]}
        </if>
        GROUP BY app.`code`
    </select>
    <!--    获取套餐已经订阅的客户-->
    <select id="getPackageSubscribeCustomer" resultType="java.util.Map">
        SELECT id,name FROM system_tenant WHERE id in(SELECT tenant_id FROM system_tenant_package_subscription WHERE package_code = #{code} and ( cdc_op IS NULL OR cdc_op != 'd') AND status =0 AND deleted =0)
    </select>
    <!--    获取服务所有客户-->
    <select id="getServiceSubscribeCustomer" resultType="java.util.Map">
        SELECT id,name FROM system_tenant WHERE id in
        <foreach collection="code" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ( cdc_op IS NULL OR cdc_op != 'd')
    </select>
    <!--    获取套餐更新记录-->
    <select id="getPackageUploadRecord" resultType="java.util.Map">
        SELECT package.id, package.name, package.code, package.ver, DATE_FORMAT(package.update_time,  '%Y-%m-%d %H:%i:%s') as update_time,  menu.id AS menu_id, menu.code AS menu_code, menu.name AS menu_name FROM system_tenant_package package LEFT JOIN system_tenant_package_menu pmenu ON package.id = pmenu.package_id LEFT JOIN system_menu menu ON menu.id =  pmenu.menu_id WHERE package.code = #{code} and ( package.cdc_op IS NULL OR package.cdc_op != 'd')  and ( pmenu.cdc_op IS NULL OR pmenu.cdc_op != 'd') AND package.deleted = 0 ORDER BY package.id DESC
    </select>
    <!--    获取服务更新记录-->
    <select id="getServiceUploadRecord" resultType="java.util.Map">
        SELECT app.id,app.name,app.code,app.status,app.create_time,app.ver, DATE_FORMAT(app.update_time,  '%Y-%m-%d %H:%i:%s') as update_time,menu.id AS menu_id,menu.name AS menu_name,menu.code AS menu_code FROM system_tenant_application app LEFT JOIN system_menu menu ON menu.application_id = app.id WHERE app.code = #{code} and ( app.cdc_op IS NULL OR app.cdc_op != 'd') and ( menu.cdc_op IS NULL OR menu.cdc_op != 'd')    </select>
    <!--    获取所有套餐和服务-->

    <select id="getAllPakSer" resultType="java.util.Map">
        SELECT * FROM ( SELECT MAX(jt.date_val), MAX( bus.id ) AS id, MAX(bus.name) as name, MAX(bus.code) as code, MAX(bus.status) as status,  MAX(bus.ver) as ver, MAX(bus.create_time) as create_time, MAX(bus.update_time) as update_time, 1 as classes , NULL AS application_owner FROM  manifest m  JOIN JSON_TABLE(JSON_EXTRACT(m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN system_tenant_package bus ON bus.CODE = m.entity_id WHERE m.entity = 'system_tenant_package' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.deleted = 0 GROUP BY  bus.CODE UNION SELECT jt.date_val, MAX( bus.id ) AS id, MAX(bus.name) as name, MAX(bus.code) as code, MAX(bus.status) as status, MAX(bus.ver) as ver, MAX(bus.create_time) as create_time, MAX(bus.update_time) as update_time, 2 AS classes, MAX(bus.application_owner) as application_owner FROM manifest m JOIN JSON_TABLE(JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt  LEFT JOIN system_tenant_application bus ON bus.code = m.entity_id WHERE  m.entity = 'system_tenant_application' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.application_owner != '' AND bus.deleted = 0 GROUP BY bus.CODE ) temp
    </select>
    <!--    获取订阅客户总量-->
    <select id="getPackageAndCustomerServiceCustomerSum" resultType="java.util.Map">
        SELECT count(DISTINCT ten.id) AS count from system_tenant ten LEFT JOIN system_tenant_application_subscription sub on sub.tenant_id = ten.id LEFT JOIN system_tenant_application app on app.id = sub.application_id WHERE ten.deleted = 0 and sub.deleted = 0 and ten.status = 0 and sub.status = 0 and ( ten.cdc_op IS NULL OR ten.cdc_op != 'd' ) and ( sub.cdc_op IS NULL OR sub.cdc_op != 'd' ) AND app.code !="INFRA"
        <if test="date_val != null">
            AND sub.update_time &gt; #{date_val[0]} AND sub.update_time &lt; #{date_val[1]}
        </if>
    </select>
    <!--    获取订阅客户总量（按月）-->
    <select id="getPackageAndCustomerServiceCustomerSumRange" resultType="java.util.Map">
        SELECT count(1) as count , month FROM ( SELECT  DATE_FORMAT(MIN(sub.update_time),'%Y-%m') as month from system_tenant ten LEFT JOIN system_tenant_application_subscription sub on sub.tenant_id = ten.id LEFT JOIN system_tenant_application app on app.id = sub.application_id WHERE ten.deleted = 0 and sub.deleted = 0 and ten.status = 0 and sub.status = 0 and ( ten.cdc_op IS NULL OR ten.cdc_op != 'd' ) and ( sub.cdc_op IS NULL OR sub.cdc_op != 'd' ) AND app.code !="INFRA"
        <if test="date_val != null">
            AND sub.update_time &gt; #{date_val[0]} AND sub.update_time &lt; #{date_val[1]}
        </if>
        GROUP BY ten.id ) as result GROUP BY month ORDER BY month
    </select>
    <!--    获取客户审计记录（客户订阅记录）-->
    <select id="getCustomerAuditRecord" resultType="java.util.Map">
        SELECT * FROM ( SELECT * FROM (
        SELECT
        ten.id AS tenant_id,
        cust.customer_id AS customer_id,
        ten.name AS tenant_name,
        MAX(pack.id) AS package_id,
        pack.code AS package_code,
        pack.name AS package_name,
        DATE_FORMAT(sub.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
        DATE_FORMAT(sub.update_time, '%Y-%m-%d %H:%i:%s') AS update_time,
        sub.status AS status,
        "1" AS classes
        FROM system_tenant ten
        LEFT JOIN system_tenant_package_subscription sub ON sub.tenant_id = ten.id
        LEFT JOIN system_tenant_package pack ON pack.code = sub.package_code
        LEFT JOIN customer_info cust ON cust.customer_code = ten.id

        WHERE ten.deleted = 0
        AND (ten.cdc_op IS NULL OR ten.cdc_op != 'd')
        AND (sub.cdc_op IS NULL OR sub.cdc_op != 'd')
        AND (pack.cdc_op IS NULL OR pack.cdc_op != 'd')
        AND (cust.cdc_op IS NULL OR cust.cdc_op != 'd')
        AND (sub.status = 0 OR sub.status IS NULL)
        GROUP BY tenant_id, package_code

        UNION ALL

        SELECT
        ten.id AS tenant_id,
        cust.customer_id AS customer_id,
        ten.name AS tenant_name,
        app.id AS package_id,
        app.code AS package_code,
        app.name AS package_name,
        DATE_FORMAT(sub.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
        DATE_FORMAT(sub.update_time, '%Y-%m-%d %H:%i:%s') AS update_time,
        sub.status AS status,
        "2" AS classes
        FROM system_tenant_application_subscription sub
        LEFT JOIN system_tenant_application app ON app.id = sub.application_id
        LEFT JOIN system_tenant ten ON ten.id = sub.tenant_id
        LEFT JOIN customer_info cust ON cust.customer_code = ten.id
        WHERE app.application_owner != ''
        AND app.deleted = 0
        AND app.status = 0
        AND sub.deleted = 0
        AND sub.status = 0
        AND ten.deleted = 0
        AND ten.status = 0
        AND (sub.cdc_op IS NULL OR sub.cdc_op != 'd')
        AND (app.cdc_op IS NULL OR app.cdc_op != 'd')
        AND (ten.cdc_op IS NULL OR ten.cdc_op != 'd')
        AND (cust.cdc_op IS NULL OR cust.cdc_op != 'd')
        ) AS results
        LEFT JOIN
        (SELECT tenant_id as tid,  'APIGW' as pc, GROUP_CONCAT(DATE_FORMAT(expire_time,'%Y-%m-%d')) as expire_time from system_api_credential  WHERE deleted = 0 GROUP BY tenant_id) sac
        on results.tenant_id = sac.tid and results.package_code = sac.pc
        WHERE 1=1
        <if test="name != null and name != ''">
            AND package_name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="customerId != null and customerId != ''">
            AND customer_id = #{customerId}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="time != null and time.size() == 2">
            AND create_time > #{time[0]} AND create_time &lt; #{time[1]}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit} ) temp
    </select>

    <!--    获取所有套餐和定制服务（不分时间）-->
    <select id="getAllPackageAndCustomerService" resultType="java.util.Map">
        SELECT * FROM ( SELECT
            MAX(jt.date_val) as date_val,
            MAX(bus.id) AS id,
            MAX(bus.name) as name,
            MAX(bus.code) as code,
            MAX(bus.status) as status,
            MAX(bus.ver) as ver,
            DATE_FORMAT(MAX(bus.create_time), '%Y-%m-%d %H:%i:%s') as create_time,
            DATE_FORMAT(MAX(bus.update_time), '%Y-%m-%d %H:%i:%s') as update_time,
            1 as classes,
            NULL AS application_owner
        FROM manifest m
        JOIN JSON_TABLE(
            JSON_EXTRACT(m.manifest, '$.created'),
            '$[*]' COLUMNS (date_val DECIMAL(16, 1) PATH '$')
        ) jt
        LEFT JOIN system_tenant_package bus ON bus.code = m.entity_id
        WHERE m.entity = 'system_tenant_package'
            AND (bus.cdc_op IS NULL OR bus.cdc_op != 'd')
            AND bus.id IS NOT NULL
            AND bus.deleted = 0
            AND bus.code NOT IN (
                'doubts_in_image_review_application',
                'base_comp_application',
                'standard_comp_application',
                'workflow_application',
                'pay_mgr_application',
                'standard_comp_application_international',
                'standard_comp_application_hk',
                'client_test'
            )
        GROUP BY bus.code

        UNION ALL

        SELECT
            MAX(jt.date_val) as date_val,
            MAX(bus.id) AS id,
            MAX(bus.name) as name,
            MAX(bus.code) as code,
            MAX(bus.status) as status,
            MAX(bus.ver) as ver,
            DATE_FORMAT(MAX(bus.create_time), '%Y-%m-%d %H:%i:%s') as create_time,
            DATE_FORMAT(MAX(bus.update_time), '%Y-%m-%d %H:%i:%s') as update_time,
            2 AS classes,
            MAX(bus.application_owner) as application_owner
        FROM manifest m
        JOIN JSON_TABLE(
            JSON_EXTRACT(m.manifest, '$.created'),
            '$[*]' COLUMNS (date_val DECIMAL(16, 1) PATH '$')
        ) jt
        LEFT JOIN system_tenant_application bus ON bus.code = m.entity_id
        WHERE m.entity = 'system_tenant_application'
            AND (bus.cdc_op IS NULL OR bus.cdc_op != 'd')
            AND bus.id IS NOT NULL
            AND bus.application_owner != ''
            AND bus.deleted = 0
            AND bus.code NOT IN (
                'doubts_in_image_review_application',
                'base_comp_application',
                'standard_comp_application',
                'workflow_application',
                'pay_mgr_application',
                'standard_comp_application_international',
                'standard_comp_application_hk',
                'client_test'
            )
        GROUP BY bus.code
        ORDER BY classes, code ) temp
    </select>

    <!--    获取客户审计记录总数（客户订阅记录总数）-->
    <select id="getCustomerAuditRecordCount" resultType="java.util.Map">
        SELECT * FROM ( SELECT COUNT(*) as total FROM (
        SELECT
        ten.id AS tenant_id,
        cust.customer_id AS customer_id,
        ten.name AS tenant_name,
        MAX(pack.id) AS package_id,
        pack.code AS package_code,
        pack.name AS package_name,
        sub.create_time AS create_time,
        sub.update_time AS update_time,
        sub.status AS status,
        "1" AS classes
        FROM system_tenant ten
        LEFT JOIN system_tenant_package_subscription sub ON sub.tenant_id = ten.id
        LEFT JOIN system_tenant_package pack ON pack.code = sub.package_code
        LEFT JOIN customer_info cust ON cust.customer_code = ten.id
        WHERE ten.deleted = 0
        AND (ten.cdc_op IS NULL OR ten.cdc_op != 'd')
        AND (sub.cdc_op IS NULL OR sub.cdc_op != 'd')
        AND (pack.cdc_op IS NULL OR pack.cdc_op != 'd')
        AND (cust.cdc_op IS NULL OR cust.cdc_op != 'd')
        AND (sub.status = 0 OR sub.status IS NULL)
        GROUP BY tenant_id, package_code

        UNION ALL

        SELECT
        ten.id AS tenant_id,
        cust.customer_id AS customer_id,
        ten.name AS tenant_name,
        app.id AS package_id,
        app.code AS package_code,
        app.name AS package_name,
        sub.create_time AS create_time,
        sub.update_time AS update_time,
        sub.status AS status,
        "2" AS classes
        FROM system_tenant_application_subscription sub
        LEFT JOIN system_tenant_application app ON app.id = sub.application_id
        LEFT JOIN system_tenant ten ON ten.id = sub.tenant_id
        LEFT JOIN customer_info cust ON cust.customer_code = ten.id
        WHERE app.application_owner != ''
        AND app.deleted = 0
        AND app.status = 0
        AND sub.deleted = 0
        AND sub.status = 0
        AND ten.deleted = 0
        AND ten.status = 0
        AND (sub.cdc_op IS NULL OR sub.cdc_op != 'd')
        AND (app.cdc_op IS NULL OR app.cdc_op != 'd')
        AND (ten.cdc_op IS NULL OR ten.cdc_op != 'd')
        AND (cust.cdc_op IS NULL OR cust.cdc_op != 'd')
        ) AS results
        WHERE 1=1
        <if test="name != null and name != ''">
            AND package_name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="customerId != null and customerId != ''">
            AND customer_id = #{customerId}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="time != null and time.size() == 2">
            AND create_time > #{time[0]} AND create_time &lt; #{time[1]}
        </if>
        ) temp
    </select>

    <!--    获取所有最新套餐（只有套餐）-->
    <select id="getAllNewPackage" resultType="java.util.Map">
        SELECT
            MAX(id) as id,
            code,
            MAX(name) as name
        FROM system_tenant_package
        WHERE deleted = 0
            AND (cdc_op IS NULL OR cdc_op != 'd')
            AND status = 0
        GROUP BY code
    </select>

    <!--    通过租户查询已经订阅套餐和服务-->
    <select id="getAllNewPackageAndServiceByTenantId" resultType="java.util.Map">
        SELECT * FROM ( SELECT
            id,
            tenant_id,
            package_id,
            '' as package_name,
            package_code,
            DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
            '1' AS classes
        FROM system_tenant_package_subscription
        WHERE tenant_id = #{tenantId}
            AND deleted = 0
            AND (cdc_op IS NULL OR cdc_op != 'd')

        UNION ALL

        SELECT
            sub.id,
            sub.tenant_id,
            app.id AS package_id,
            app.name as package_name,
            app.code AS package_code,
            DATE_FORMAT(sub.update_time, '%Y-%m-%d %H:%i:%s') AS update_time,
            DATE_FORMAT(sub.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
            '2' AS classes
        FROM system_tenant_application_subscription sub
        LEFT JOIN system_tenant_application app ON app.id = sub.application_id
        WHERE sub.tenant_id = #{tenantId}
            AND sub.deleted = 0
            AND sub.status = 0
            AND app.application_owner != ''
            AND (sub.cdc_op IS NULL OR sub.cdc_op != 'd')
            AND (app.cdc_op IS NULL OR app.cdc_op != 'd')
        ORDER BY create_time DESC ) temp
    </select>

    <!--    获取套餐菜单列表-->
    <select id="getPackageMenu" resultType="java.util.Map">
        SELECT
            menu.id,
            menu.name,
            menu.code,
            menu.parent_id,
            pmenu.package_id,
            menu.category,
            menu.application_id,
            menu.type,
            menu.sort
        FROM system_tenant_package_menu pmenu
        LEFT JOIN system_menu menu ON menu.id = pmenu.menu_id
        WHERE pmenu.package_id = #{id}
            AND pmenu.status = 0
            AND menu.status = 0
            AND pmenu.deleted = 0
            AND menu.deleted = 0
            AND (pmenu.cdc_op IS NULL OR pmenu.cdc_op != 'd')
            AND (menu.cdc_op IS NULL OR menu.cdc_op != 'd')
    </select>

    <!--    获取服务菜单列表-->
    <select id="getServiceMenu" resultType="java.util.Map">
        SELECT
            id,
            name,
            code,
            parent_id,
            category,
            application_id,
            type,
            sort
        FROM system_menu
        WHERE application_id = #{id}
            AND deleted = 0
            AND status = 0
            AND (cdc_op IS NULL OR cdc_op != 'd')
    </select>
</mapper>