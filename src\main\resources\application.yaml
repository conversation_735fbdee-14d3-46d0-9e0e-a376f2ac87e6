spring:
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类


--- #################
spring:
  application:
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure  # exclude druid auto config, use dynamic-datasource-spring-boot-starter
  jap:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  datasource:
    druid: # Druid [watch] global config
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # white list, empty means allow all access
        url-pattern: /druid/*
        login-username: # console's username & password
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # slow SQL log
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # dynamic db configuration
      druid: # Druid connect pool global related config
        # init connect count
        initial-size: 5
        # min connect count
        min-idle: 10
        # max connect count
        max-active: 20
        # wait time out, unit: millisecond
        max-wait: 600000
        # check time
        time-between-eviction-runs-millis: 60000
        # min connect live time
        min-evictable-idle-time-millis: 300000
        # max connect live time
        max-evictable-idle-time-millis: 900000
        # check connect available
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          name: umv_audit
          url: jdbc:mysql://************:${common-env.mysql.port}/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true
          username: ${common-env.mysql.username}
          password: ${common-env.mysql.password}
        slave: # simulate slave db
          name: umv_audit
          url: jdbc:mysql://************:${common-env.mysql.port}/${spring.datasource.dynamic.datasource.slave.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true
          username: ${common-env.mysql.username}
          password: ${common-env.mysql.password}
#        master:
#          name: ${common-env.mysqlProxy.dbname}
#          url: jdbc:mysql://${common-env.mysqlProxy.host}:${common-env.mysqlProxy.port}/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true
#          username: ${common-env.mysqlProxy.username}
#          password: ${common-env.mysqlProxy.password}
#        slave: # simulate slave db
#          name: ${common-env.mysqlProxy.dbname}
#          url: jdbc:mysql://${common-env.mysqlProxy.host}:${common-env.mysqlProxy.port}/${spring.datasource.dynamic.datasource.slave.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true
#          username: ${common-env.mysqlProxy.username}
#          password: ${common-env.mysqlProxy.password}

--- ############# redis配置 #############
spring:
  redis:
    host: ${common-env.redis.host}
    port: ${common-env.redis.port}
    password : ${common-env.redis.password}
    database: ${common-env.redis.database}

--- ################# authentication 配置 #################
authentication:
  permit-all-urls:
    - /**

--- ################# DB_QUERY 配置 #################
sql-executor:
  db-query:
    query-requests:
      - method: GET
        url-pattern: '/db-query/manifest/count'
        statement-id: 'default.countManifest'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackage' #获取套餐
        statement-id: 'manifest.getPackage'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getService' #获取服务
        statement-id: 'manifest.getService'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getServiceCustomerSubList' #根据服务获取客户订阅数（所有）
        statement-id: 'manifest.getServiceCustomerSubList'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageCustomerSubList' #根据套餐获取客户订阅数（所有）
        statement-id: 'manifest.getPackageCustomerSubList'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageSubscribeCustomer' #获取套餐已经订阅的客户
        statement-id: 'manifest.getPackageSubscribeCustomer'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getServiceSubscribeCustomer' #获取服务已经订阅的客户
        statement-id: 'manifest.getServiceSubscribeCustomer'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageUploadRecord' #获取套餐更新记录
        statement-id: 'manifest.getPackageUploadRecord'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getServiceUploadRecord' #获取套餐更新记录
        statement-id: 'manifest.getServiceUploadRecord'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageAndCustomerServiceCustomerSum' #获取所有套餐和定制服务的客户总量统计数据
        statement-id: 'manifest.getPackageAndCustomerServiceCustomerSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageAndCustomerServiceBusinessSum' #获取所有套餐和定制服务的业务总量统计数据
        statement-id: 'manifest.getPackageAndCustomerServiceBusinessSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getAllPakSer' #获取套餐更新记录
        statement-id: 'manifest.getAllPakSer'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageAndCustomerServiceCustomerSum' #获取所有订阅客户总量
        statement-id: 'manifest.getPackageAndCustomerServiceCustomerSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getPackageAndCustomerServiceCustomerSumRange' #获取所有订阅客户总量（按月）
        statement-id: 'manifest.getPackageAndCustomerServiceCustomerSumRange'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。

      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOnlineDesignApplication' #获取在线设计业务数据
        statement-id: 'package.getBusDataByOnlineDesignApplication'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOnlineOrderingApplication' #获取在线下单业务数据
        statement-id: 'package.getBusDataByOnlineOrderingApplication'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOrderGlobalApp' #获取在线下单_国际版业务数据
        statement-id: 'package.getBusDataByOrderGlobalApp'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByDIY' #获取DIY业务数据
        statement-id: 'package.getBusDataByDIY'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByImageReviewApplication' #获取图像审核业务数据
        statement-id: 'package.getBusDataByImageReviewApplication'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByReportManagementApplication' #获取报表管理业务数据
        statement-id: 'package.getBusDataByReportManagementApplication'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByInventoryManagementApplication' #获取库存管理业务数据
        statement-id: 'package.getBusDataByInventoryManagementApplication'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByStockGlobalApp' #获取库存管理_国际版业务数据
        statement-id: 'package.getBusDataByStockGlobalApp'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByCGFBB' #获取广发库存定制服务业务数据
        statement-id: 'package.getBusDataByCGFBB'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByCZJNS' #获取浙江农信报表定制服务业务数据
        statement-id: 'package.getBusDataByCZJNS'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。 --------------------------------
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOnlineDesignApplicationByMonth' #获取在线设计业务数据 以下为按月统计
        statement-id: 'package.getBusDataByOnlineDesignApplicationByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOnlineOrderingApplicationByMonth' #获取在线下单业务数据
        statement-id: 'package.getBusDataByOnlineOrderingApplicationByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOrderGlobalAppByMonth' #获取在线下单_国际版业务数据
        statement-id: 'package.getBusDataByOrderGlobalAppByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByDIYByMonth' #获取DIY业务数据
        statement-id: 'package.getBusDataByDIYByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByImageReviewApplicationByMonth' #获取图像审核业务数据
        statement-id: 'package.getBusDataByImageReviewApplicationByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByReportManagementApplicationByMonth' #获取报表管理业务数据
        statement-id: 'package.getBusDataByReportManagementApplicationByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByInventoryManagementApplicationByMonth' #获取库存管理业务数据
        statement-id: 'package.getBusDataByInventoryManagementApplicationByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByStockGlobalAppByMonth' #获取库存管理_国际版业务数据
        statement-id: 'package.getBusDataByStockGlobalAppByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByCGFBBByMonth' #获取广发库存定制服务业务数据
        statement-id: 'package.getBusDataByCGFBBByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByCZJNSByMonth' #获取浙江农信报表定制服务业务数据
        statement-id: 'package.getBusDataByCZJNSByMonth'
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。 ---------------------------------------
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOnlineDesignApplicationSum' #获取在线设计业务数据 以下为获取业务总数
        statement-id: 'package.getBusDataByOnlineDesignApplicationSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOnlineOrderingApplicationSum' #获取在线下单业务数据
        statement-id: 'package.getBusDataByOnlineOrderingApplicationSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByOrderGlobalAppSum' #获取在线下单_国际版业务数据
        statement-id: 'package.getBusDataByOrderGlobalAppSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByDIYSum' #获取DIY业务数据
        statement-id: 'package.getBusDataByDIYSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByImageReviewApplicationSum' #获取图像审核业务数据
        statement-id: 'package.getBusDataByImageReviewApplicationSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByReportManagementApplicationSum' #获取报表管理业务数据
        statement-id: 'package.getBusDataByReportManagementApplicationSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByInventoryManagementApplicationSum' #获取库存管理业务数据
        statement-id: 'package.getBusDataByInventoryManagementApplicationSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByStockGlobalAppSum' #获取库存管理_国际版业务数据
        statement-id: 'package.getBusDataByStockGlobalAppSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByCGFBBSum' #获取广发库存定制服务业务数据
        statement-id: 'package.getBusDataByCGFBBSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: '/db-query/manifest/getBusDataByCZJNSSum' #获取浙江农信报表定制服务业务数据
        statement-id: 'package.getBusDataByCZJNSSum'
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。 --------------------------------


      ################# DB_QUERY 配置  以下是 客户审计#################
      - method: POST
        url-pattern: "/db-query/manifest/getCustomerAuditRecord" #获取客户审计记录
        statement-id: "manifest.getCustomerAuditRecord"
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: "/db-query/manifest/getCustomerAuditRecordCount" #获取客户审计记录总数
        statement-id: "manifest.getCustomerAuditRecordCount"
        result-type: ONE # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: "/db-query/manifest/getAllPackageAndCustomerService" #获取所有套餐和定制服务
        statement-id: "manifest.getAllPackageAndCustomerService"
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: "/db-query/manifest/getAllNewPackage" #获取所有最新套餐
        statement-id: "manifest.getAllNewPackage"
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: "/db-query/manifest/getAllNewPackageAndServiceByTenantId" #通过租户查询已订阅套餐和服务
        statement-id: "manifest.getAllNewPackageAndServiceByTenantId"
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: "/db-query/manifest/getPackageMenu" #获取套餐菜单列表
        statement-id: "manifest.getPackageMenu"
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
      - method: POST
        url-pattern: "/db-query/manifest/getServiceMenu" #获取服务菜单列表
        statement-id: "manifest.getServiceMenu"
        result-type: LIST # ONE：查询结果是单条记录，LIST：查询结果是多条记录。
