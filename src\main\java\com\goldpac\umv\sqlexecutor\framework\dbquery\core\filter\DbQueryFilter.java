package com.goldpac.umv.sqlexecutor.framework.dbquery.core.filter;

import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import com.goldpac.umv.framework.exception.ServiceException;
import com.goldpac.umv.framework.exception.enums.GlobalErrorCodeConstants;
import com.goldpac.umv.framework.pojo.CommonResult;
import com.goldpac.umv.sqlexecutor.framework.dbquery.config.DbQueryProperties;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.annotation.BypassDbQueryFilter;
import com.goldpac.umv.sqlexecutor.utils.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @created at 2025/7/8 星期二
 */
@Slf4j
public class DbQueryFilter extends OncePerRequestFilter {

    private static AntPathMatcher antPathMatcher = new AntPathMatcher();

    private final DbQueryProperties properties;

    private final QueryRequestExecutor queryRequestExecutor;

    private final RequestMappingHandlerMapping handlerMapping;

    public DbQueryFilter(DbQueryProperties properties, QueryRequestExecutor queryRequestExecutor,
            RequestMappingHandlerMapping handlerMapping) {
        this.properties = properties;
        this.queryRequestExecutor = queryRequestExecutor;
        this.handlerMapping = handlerMapping;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            ServletUtils.writeJSON(response, queryRequestExecutor.execute(request));
        } catch (Throwable ex) {
            log.error("[doFilterInternal][请求处理异常]", ex);
            if (ex instanceof ServiceException) {
                ServletUtils.writeJSON(response, CommonResult.error((ServiceException) ex));
                return;
            }
            ServletUtils.writeJSON(response, CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR));
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        try {
            HandlerExecutionChain handler = handlerMapping.getHandler(request);
            if (handler != null) {
                Object handlerObject = handler.getHandler();
                if (handlerObject instanceof HandlerMethod) {
                    HandlerMethod handlerMethod = (HandlerMethod) handlerObject;
                    // 检查方法或类上是否存在 @BypassDbQueryFilter 注解
                    if (handlerMethod.hasMethodAnnotation(BypassDbQueryFilter.class) ||
                            handlerMethod.getBeanType().isAnnotationPresent(BypassDbQueryFilter.class)) {
                        return true; // 如果存在注解，则不进行过滤
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查 @BypassDbQueryFilter 注解时出错", e);
        }

        // 如果没有注解，则执行原有的 URL 匹配逻辑
        return !isMatch(request);
    }

    private boolean isMatch(HttpServletRequest request) {
        String url = RequestUtils.getRequestPath(request);
        for (String pattern : properties.getDbQueryUrlPatterns()) {
            if (antPathMatcher.match(pattern, url)) {
                return true;
            }
        }
        return false;
    }

}
