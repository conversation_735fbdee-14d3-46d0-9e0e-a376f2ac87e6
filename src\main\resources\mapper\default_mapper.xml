<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="default">

    <select id="countManifest" resultType="java.util.Map">
        SELECT COUNT(1) AS total FROM manifest where 1 = 1
        <if test="entityId != null">
            and entity_id = #{entityId}
        </if>
        <if test="entityIds != null">
            and entity_id in
            <foreach collection="entityIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>