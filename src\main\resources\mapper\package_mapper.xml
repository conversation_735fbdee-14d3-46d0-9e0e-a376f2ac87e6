<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="package">

<!--    业务数据-->

<!--    获取在线设计业务数据-->
    <select id="getBusDataByOnlineDesignApplication" resultType="java.util.Map">
        SELECT COUNT(DISTINCT makecard_requirement_info_id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN makecard_requirement_info    bus ON bus.makecard_requirement_info_id = m.entity_id WHERE m.entity = 'makecard_requirement_info'
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.makecard_requirement_info_id IS NOT NULL AND bus.makecard_requirement_info_deleted = 0 AND makecard_requirement_info_phase != 14
    </select>
<!--    获取在线下单业务数据-->
    <select id="getBusDataByOnlineOrderingApplication" resultType="java.util.Map">
        SELECT COUNT(DISTINCT id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_order bus ON bus.id = m.entity_id WHERE m.entity = 'review_order'
                                                                                                                                                                                                                                  <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.status != 'REVIEW_CANCEL' AND bus.status != 'CANCELLED' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.customer_id in (SELECT customer_id FROM customer_info where delivery_method = 1) and ( cdc_op IS NULL OR cdc_op != 'd')
    </select>
<!--    获取在线下单_国际版业务数据-->
    <select id="getBusDataByOrderGlobalApp" resultType="java.util.Map">
        SELECT COUNT(DISTINCT id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_order bus ON bus.id = m.entity_id WHERE m.entity = 'review_order'                                                                                                                                                                                                                                   <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL AND bus.status != 'REVIEW_CANCEL' AND bus.status != 'CANCELLED' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.customer_id in (SELECT customer_id FROM customer_info where delivery_method = 3 OR delivery_method = 5 ) and ( cdc_op IS NULL OR cdc_op != 'd')    </select>
<!--    获取DIY业务数据-->
    <select id="getBusDataByDIY" resultType="java.util.Map">
        SELECT COUNT(DISTINCT id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN order_diy_order  bus ON bus.id = m.entity_id WHERE m.entity = 'order_diy_order'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.order_status != 'CLOSE_ORDER' AND bus.order_status != 'CANCELLED' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')    </select>
<!--    获取图像审核业务数据-->
    <select id="getBusDataByImageReviewApplication" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  order_id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_audit_order bus ON bus.order_id = m.entity_id WHERE m.entity = 'review_audit_order'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.order_id IS NOT NULL and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')    </select>
<!--    获取报表管理业务数据-->
    <select id="getBusDataByReportManagementApplication" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN tab_mail bus ON bus.id = m.entity_id WHERE m.entity = 'tab_mail'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.id IS NOT NULL    </select>
<!--    获取库存管理业务数据-->
    <select id="getBusDataByInventoryManagementApplication" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN stock_warning_send_record bus ON bus.id = m.entity_id WHERE m.entity = 'stock_warning_send_record' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND customer_code in (SELECT customer_code COLLATE utf8mb4_general_ci AS tenant_id FROM customer_info where delivery_method = 1 and (cdc_op IS NULL OR cdc_op != 'd') )    </select>
<!--    获取库存管理_国际版业务数据-->
    <select id="getBusDataByStockGlobalApp" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN stock_warning_send_record bus ON bus.id = m.entity_id WHERE m.entity = 'stock_warning_send_record' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND customer_code in (SELECT customer_code COLLATE utf8mb4_general_ci AS tenant_id FROM customer_info where (delivery_method = 3 OR delivery_method = 5) and (cdc_op IS NULL OR cdc_op != 'd') )    </select>
<!--    获取广发库存定制服务业务数据-->
    <select id="getBusDataByCGFBB" resultType="java.util.Map">
        SELECT COUNT(DISTINCT bus.report_date) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN product_wms_daily_stock bus ON bus.id = m.entity_id WHERE m.entity = 'product_wms_daily_stock' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')          <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL    </select>
<!--    获取浙江农信报表定制服务业务数据-->
    <select id="getBusDataByCZJNS" resultType="java.util.Map">
        SELECT COUNT(DISTINCT DATE_FORMAT( bus.create_time, '%Y-%m-%d')) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN order_zjns_report bus ON bus.id = m.entity_id WHERE m.entity = 'order_zjns_report'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')    </select>

<!--    按月统计-->

<!--    获取在线设计业务数据-->
    <select id="getBusDataByOnlineDesignApplicationByMonth" resultType="java.util.Map">
        SELECT DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count   FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN makecard_requirement_info    bus ON bus.makecard_requirement_info_id = m.entity_id WHERE m.entity = 'makecard_requirement_info' AND makecard_requirement_info_phase != 14         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')  AND bus.makecard_requirement_info_id IS NOT NULL AND bus.makecard_requirement_info_deleted = 0  GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取在线下单业务数据-->
    <select id="getBusDataByOnlineOrderingApplicationByMonth" resultType="java.util.Map">
        SELECT DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count  FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_order bus ON bus.id = m.entity_id WHERE m.entity = 'review_order'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.status != 'REVIEW_CANCEL' AND bus.status != 'CANCELLED' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.customer_id in (SELECT customer_id FROM customer_info where delivery_method = 1 and ( cdc_op IS NULL OR cdc_op != 'd') ) GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取在线下单_国际版业务数据-->
    <select id="getBusDataByOrderGlobalAppByMonth" resultType="java.util.Map">
        SELECT DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count  FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_order bus ON bus.id = m.entity_id WHERE m.entity = 'review_order'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.status != 'REVIEW_CANCEL' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.status != 'CANCELLED' AND bus.customer_id in (SELECT customer_id FROM customer_info where (delivery_method = 3 OR delivery_method = 5) and ( cdc_op IS NULL OR cdc_op != 'd')  ) GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取DIY业务数据-->
    <select id="getBusDataByDIYByMonth" resultType="java.util.Map">
        SELECT DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN  order_diy_order bus ON bus.id = m.entity_id WHERE m.entity = 'order_diy_order'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.order_status != 'CLOSE_ORDER' AND bus.order_status != 'CANCELLED' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取图像审核业务数据-->
    <select id="getBusDataByImageReviewApplicationByMonth" resultType="java.util.Map">
        SELECT DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count   FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_audit_order bus ON bus.order_id = m.entity_id WHERE m.entity = 'review_audit_order'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.order_id IS NOT NULL  GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取报表管理业务数据-->
    <select id="getBusDataByReportManagementApplicationByMonth" resultType="java.util.Map">
        SELECT DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN tab_mail bus ON bus.id = m.entity_id WHERE m.entity = 'tab_mail'         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND  bus.id IS NOT NULL GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取库存管理业务数据-->
    <select id="getBusDataByInventoryManagementApplicationByMonth" resultType="java.util.Map">
        SELECT  DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(DISTINCT bus.id) as count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN stock_warning_send_record bus ON bus.id = m.entity_id WHERE m.entity = 'stock_warning_send_record' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')          <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL  AND customer_code in (SELECT customer_code COLLATE utf8mb4_general_ci AS tenant_id FROM customer_info where delivery_method = 1 and (cdc_op IS NULL OR cdc_op != 'd') )   GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取库存管理_国际版业务数据-->
    <select id="getBusDataByStockGlobalAppByMonth" resultType="java.util.Map">
        SELECT  DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,count(*) as count  FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN stock_warning_send_record bus ON bus.id = m.entity_id WHERE m.entity = 'stock_warning_send_record' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')         <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND customer_code in (SELECT customer_code COLLATE utf8mb4_general_ci AS tenant_id FROM customer_info where (delivery_method = 3 OR delivery_method = 5) and (cdc_op IS NULL OR cdc_op != 'd') ) GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>
<!--    获取广发库存定制服务业务数据-->
    <select id="getBusDataByCGFBBByMonth" resultType="java.util.Map">
        SELECT  DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,COUNT( DISTINCT bus.report_date ) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN product_wms_daily_stock bus ON bus.id = m.entity_id WHERE m.entity = 'product_wms_daily_stock' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')  <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month</select>
<!--    获取浙江农信报表定制服务业务数据-->
    <select id="getBusDataByCZJNSByMonth" resultType="java.util.Map">
        SELECT  DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') as month ,COUNT(DISTINCT DATE_FORMAT( bus.create_time, '%Y-%m-%d')) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN order_zjns_report bus ON bus.id = m.entity_id WHERE m.entity = 'order_zjns_report' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')          <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL GROUP BY DATE_FORMAT(FROM_UNIXTIME(jt.date_val - 28800), '%Y-%m') ORDER BY month    </select>

<!--获取业务总数-->

<!--    在线设计-->
    <select id="getBusDataByOnlineDesignApplicationSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT makecard_requirement_info_id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN makecard_requirement_info    bus ON bus.makecard_requirement_info_id = m.entity_id WHERE m.entity = 'makecard_requirement_info'
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.makecard_requirement_info_id IS NOT NULL AND bus.makecard_requirement_info_deleted = 0 AND makecard_requirement_info_phase != 14 and bus.makecard_requirement_info_cid = #{code}
    </select>
<!--    在线下单-->
    <select id="getBusDataByOnlineOrderingApplicationSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_order   bus ON bus.id = m.entity_id WHERE m.entity = 'review_order' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')
        <if test="date_val != null">
            AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
        </if>
        AND bus.id IS NOT NULL AND bus.status != 'REVIEW_CANCEL' AND bus.status != 'CANCELLED' and bus.customer_id = #{code}    </select>
<!--    在线下单国际版-->
    <select id="getBusDataByOrderGlobalAppSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_order   bus ON bus.id = m.entity_id WHERE m.entity = 'review_order' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')
    <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL AND bus.status != 'REVIEW_CANCEL' AND bus.status != 'CANCELLED' and bus.customer_id = #{code}    </select>
<!--    DIY-->
    <select id="getBusDataByDIYSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN order_diy_order  bus ON bus.id = m.entity_id WHERE m.entity = 'order_diy_order'         <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL AND bus.order_status != 'CLOSE_ORDER' AND bus.order_status != 'CANCELLED' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')  and bus.customer_id = #{code}   </select>
<!--    图像审核-->
    <select id="getBusDataByImageReviewApplicationSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  order_id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN review_audit_order bus ON bus.order_id = m.entity_id WHERE m.entity = 'review_audit_order'         <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.order_id IS NOT NULL and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')  and bus.customer_id = #{code}   </select>
<!--    报表管理-->
    <select id="getBusDataByReportManagementApplicationSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN tab_mail bus ON bus.id = m.entity_id WHERE m.entity = 'tab_mail'         <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd') AND bus.id IS NOT NULL  AND bus.customer_code = #{code}   </select>
<!--   库存管理-->
    <select id="getBusDataByInventoryManagementApplicationSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN stock_warning_send_record bus ON bus.id = m.entity_id WHERE m.entity = 'stock_warning_send_record' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')  <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL  AND bus.customer_code = #{code}   </select>
<!--    库存管理国际版-->
    <select id="getBusDataByStockGlobalAppSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT  id) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN stock_warning_send_record bus ON bus.id = m.entity_id WHERE m.entity = 'stock_warning_send_record' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')   <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL  AND bus.customer_code = #{code} </select>
<!--    广发定制服务-->
    <select id="getBusDataByCGFBBSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT bus.report_date) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN product_wms_daily_stock bus ON bus.id = m.entity_id WHERE m.entity = 'product_wms_daily_stock' and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')          <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL   and bus.customer_code = #{code}  </select>
<!--    浙江定制服务-->
    <select id="getBusDataByCZJNSSum" resultType="java.util.Map">
        SELECT COUNT(DISTINCT DATE_FORMAT( bus.create_time, '%Y-%m-%d')) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN order_zjns_report bus ON bus.id = m.entity_id WHERE m.entity = 'order_zjns_report'         <if test="date_val != null">
        AND jt.date_val &gt; #{date_val[0]} AND jt.date_val &lt; #{date_val[1]}
    </if>
        AND bus.id IS NOT NULL and ( bus.cdc_op IS NULL OR bus.cdc_op != 'd')    </select>

</mapper>