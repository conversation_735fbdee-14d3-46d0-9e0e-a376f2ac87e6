package com.goldpac.umv.sqlexecutor.framework.dbquery.core.executor;

import cn.hutool.core.map.MapUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created at 2025/7/8 星期二
 */
@Slf4j
@AllArgsConstructor
public class DefaultMybatisStatementExecutor implements MybatisStatementExecutor {

    private final SqlSessionFactory sqlSessionFactory;

    @Override
    public Map<String, Object> executeSelectOne(String statementId, Map<String, Object> params) {
        StopWatch sw = new StopWatch();
        sw.start();
        try (SqlSession session = sqlSessionFactory.openSession()) {
            if (MapUtil.isNotEmpty(params)) {
                return session.selectOne(statementId, params);
            }
            return session.selectOne(statementId);
        }
        finally {
            sw.stop();
            log.info("[executeSelectOne][执行statementId({})完成，总耗时：{}ms]", statementId, sw.getTotalTimeMillis());
        }
    }

    @Override
    public List<Map<String, Object>> executeSelect(String statementId, Map<String, Object> params) {
        StopWatch sw = new StopWatch();
        sw.start();
        try (SqlSession session = sqlSessionFactory.openSession()) {
            if (MapUtil.isNotEmpty(params)) {
                return session.selectList(statementId, params);
            }
            return session.selectList(statementId);
        }
        finally {
            sw.stop();
            log.info("[executeSelect][执行statementId({})完成，总耗时：{}ms]", statementId, sw.getTotalTimeMillis());
        }
    }

}
