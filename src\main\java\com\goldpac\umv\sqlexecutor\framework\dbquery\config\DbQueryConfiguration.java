package com.goldpac.umv.sqlexecutor.framework.dbquery.config;

import cn.iocoder.yudao.framework.common.enums.WebFilterOrderEnum;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.executor.DefaultMybatisStatementExecutor;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.executor.MybatisStatementExecutor;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.filter.DbQueryFilter;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.filter.QueryRequestExecutor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

/**
 * <AUTHOR>
 * @created at 2025/7/8 星期二
 */
@Configuration
@EnableConfigurationProperties(DbQueryProperties.class)
public class DbQueryConfiguration {

    public static final Integer DB_QUERY_FILTER_ORDER = WebFilterOrderEnum.JWT_AUTHENTICATION_FILTER + 100;

    @Bean
    public MybatisStatementExecutor mybatisStatementExecutor(SqlSessionFactory sqlSessionFactory) {
        return new DefaultMybatisStatementExecutor(sqlSessionFactory);
    }

    @Bean
    public QueryRequestExecutor queryRequestExecutor(DbQueryProperties properties, MybatisStatementExecutor mybatisStatementExecutor) {
        return new QueryRequestExecutor(properties, mybatisStatementExecutor);
    }

    @Bean
    public FilterRegistrationBean<DbQueryFilter> dbQueryFilterBean(DbQueryProperties properties, QueryRequestExecutor queryRequestExecutor, RequestMappingHandlerMapping handlerMapping) {
        DbQueryFilter filter = new DbQueryFilter(properties, queryRequestExecutor, handlerMapping);
        FilterRegistrationBean<DbQueryFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(filter);
        bean.setOrder(DB_QUERY_FILTER_ORDER);
        return bean;
    }

}
