package com.goldpac.umv.sqlexecutor.service;

import com.goldpac.umv.framework.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户服务
 * 用于处理客户相关的业务逻辑
 */
@Slf4j
@Service
public class CustomerService {

    @Autowired
    private SqlSession sqlSession;

    /**
     * 根据查询条件查询客户信息
     *
     * @param params 查询参数
     * @return 客户信息列表
     */
    public CommonResult<Object> findCustomers(Map<String, Object> params) {
        try {
            // 处理交付方式参数
            if (params.get("deliveryMethod") != null) {
                String deliveryMethodStr = params.get("deliveryMethod").toString();
                if (!deliveryMethodStr.trim().isEmpty()) {
                    String[] methods = deliveryMethodStr.split(",");
                    Integer[] deliveryMethods = new Integer[methods.length];
                    for (int i = 0; i < methods.length; i++) {
                        deliveryMethods[i] = Integer.parseInt(methods[i].trim());
                    }
                    params.put("deliveryMethod", deliveryMethods);
                }
            }

            List<Map<String, Object>> result = sqlSession.selectList("customer.findCustomers", params);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询客户信息失败: {}", e.getMessage(), e);
            return CommonResult.error(500, "查询客户信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据公司类型统计客户数量
     *
     * @param params 查询参数
     * @return 统计结果
     */
    public CommonResult<Object> countCustomersByType(Map<String, Object> params) {
        try {
            // 处理公司类型参数
            if (params.get("companyType") != null) {
                String companyTypeStr = params.get("companyType").toString();
                if (!companyTypeStr.trim().isEmpty()) {
                    String[] types = companyTypeStr.split(",");
                    Integer[] companyTypes = new Integer[types.length];
                    for (int i = 0; i < types.length; i++) {
                        companyTypes[i] = Integer.parseInt(types[i].trim());
                    }
                    params.put("companyType", companyTypes);
                }
            }

            // 计算总行数量（branchType = '0'）
            Map<String, Object> headParams = new HashMap<>(params);
            headParams.put("branchType", "0");
            Map<String, Object> headCountResult = sqlSession.selectOne("customer.countCustomersByType", headParams);
            int headCount = headCountResult != null ? ((Number) headCountResult.get("count")).intValue() : 0;

            // 计算分行数量（branchType = '1'）
            Map<String, Object> branchParams = new HashMap<>(params);
            branchParams.put("branchType", "1");
            Map<String, Object> branchCountResult = sqlSession.selectOne("customer.countCustomersByType", branchParams);
            int branchCount = branchCountResult != null ? ((Number) branchCountResult.get("count")).intValue() : 0;

            // 计算总数
            int total = headCount + branchCount;

            // 返回结果（按照 Node.js 版本的格式）
            Map<String, Object> result = new HashMap<>();
            result.put("head", headCount);
            result.put("branch", branchCount);
            result.put("total", total);

            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("统计客户数量失败: {}", e.getMessage(), e);
            return CommonResult.error(500, "统计客户数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户节点树
     *
     * @param params 查询参数
     * @return 客户节点树
     */
    public CommonResult<Object> getCustomerNodeTree(Map<String, Object> params) {
        try {
            String customerId = (String) params.get("customerId");
            String customerCode = (String) params.get("customerCode");

            if (customerId == null && customerCode == null) {
                return CommonResult.error(400, "customerId 或 customerCode 必须提供一个");
            }

            // 查找指定的客户
            Map<String, Object> customer = null;
            if (customerId != null) {
                Map<String, Object> queryParams = new HashMap<>();
                queryParams.put("customerId", customerId);
                customer = sqlSession.selectOne("customer.findCustomerById", queryParams);
            } else {
                Map<String, Object> queryParams = new HashMap<>();
                queryParams.put("customerCode", customerCode);
                customer = sqlSession.selectOne("customer.findCustomerByCode", queryParams);
            }

            if (customer == null) {
                return CommonResult.error(404, "未找到指定的客户");
            }

            // 构建节点树
            Map<String, Object> nodeTree = buildNodeTree(customer);
            return CommonResult.success(nodeTree);

        } catch (Exception e) {
            log.error("获取客户节点树失败: {}", e.getMessage(), e);
            return CommonResult.error(500, "获取客户节点树失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户节点树列表
     *
     * @param params 查询参数
     * @return 客户节点树列表
     */
    public CommonResult<Object> getCustomerNodeTreeList(Map<String, Object> params) {
        try {
            // 处理数组参数
            processArrayParams(params);

            List<Map<String, Object>> customers = sqlSession.selectList("customer.findCustomersForNodeTree", params);

            // 为每个客户构建节点树
            List<Map<String, Object>> nodeTreeList = new ArrayList<>();
            for (Map<String, Object> customer : customers) {
                Map<String, Object> nodeTree = buildNodeTree(customer);
                nodeTreeList.add(nodeTree);
            }

            return CommonResult.success(nodeTreeList);

        } catch (Exception e) {
            log.error("获取客户节点树列表失败: {}", e.getMessage(), e);
            return CommonResult.error(500, "获取客户节点树列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户节点树列表（简化信息，树形结构，避免重复，支持分页）
     *
     * @param params 查询参数
     * @return 分页的客户节点树列表
     */
    public CommonResult<Object> getSimpleCustomerNodeTreeList(Map<String, Object> params) {
        try {
            // 处理数组参数
            processArrayParams(params);

            // 先获取所有符合条件的客户
            List<Map<String, Object>> allCustomers = sqlSession.selectList("customer.findCustomersForNodeTree", params);

            // 找出根节点客户（没有父级在查询结果中的客户）
            List<Map<String, Object>> rootCustomers = findRootCustomers(allCustomers);

            // 设置分页参数
            int page = params.get("page") != null ? Integer.parseInt(params.get("page").toString()) : 1;
            int pageSize = params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 20;
            int skip = (page - 1) * pageSize;
            int total = rootCustomers.size();

            // 对根节点进行分页
            List<Map<String, Object>> paginatedRootCustomers = rootCustomers.stream()
                    .skip(skip)
                    .limit(pageSize)
                    .collect(Collectors.toList());

            // 为每个根节点构建完整的树形结构
            List<Map<String, Object>> customerNodeTrees = new ArrayList<>();
            for (Map<String, Object> customer : paginatedRootCustomers) {
                Map<String, Object> nodeTree = buildSimpleNodeTree(customer);
                customerNodeTrees.add(nodeTree);
            }

            // 构建分页结果（按照 Node.js 版本的 PaginatedSimpleCustomerNodeDto 格式）
            Map<String, Object> result = new HashMap<>();
            result.put("items", customerNodeTrees);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", (int) Math.ceil((double) total / pageSize));
            result.put("hasNext", page < (int) Math.ceil((double) total / pageSize));
            result.put("hasPrev", page > 1);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("获取简化客户节点树列表失败: {}", e.getMessage(), e);
            return CommonResult.error(500, "获取简化客户节点树列表失败: " + e.getMessage());
        }
    }

    /**
     * 处理数组参数
     */
    private void processArrayParams(Map<String, Object> params) {
        // 处理客户ID列表
        if (params.get("customerIds") != null) {
            String customerIdsStr = params.get("customerIds").toString();
            if (!customerIdsStr.trim().isEmpty()) {
                String[] ids = customerIdsStr.split(",");
                for (int i = 0; i < ids.length; i++) {
                    ids[i] = ids[i].trim();
                }
                params.put("customerIds", ids);
            }
        }

        // 处理客户编码列表
        if (params.get("customerCodes") != null) {
            String customerCodesStr = params.get("customerCodes").toString();
            if (!customerCodesStr.trim().isEmpty()) {
                String[] codes = customerCodesStr.split(",");
                for (int i = 0; i < codes.length; i++) {
                    codes[i] = codes[i].trim();
                }
                params.put("customerCodes", codes);
            }
        }

        // 处理交付方式列表
        if (params.get("deliveryMethod") != null) {
            String deliveryMethodStr = params.get("deliveryMethod").toString();
            if (!deliveryMethodStr.trim().isEmpty()) {
                String[] methods = deliveryMethodStr.split(",");
                Integer[] deliveryMethods = new Integer[methods.length];
                for (int i = 0; i < methods.length; i++) {
                    deliveryMethods[i] = Integer.parseInt(methods[i].trim());
                }
                params.put("deliveryMethod", deliveryMethods);
            }
        }
    }

    /**
     * 递归构建节点树
     *
     * @param customer 当前客户
     * @return 节点树
     */
    private Map<String, Object> buildNodeTree(Map<String, Object> customer) {
        Map<String, Object> node = new HashMap<>(customer);
        List<Map<String, Object>> children = new ArrayList<>();

        String branchType = (String) customer.get("branch_type");
        String customerCode = (String) customer.get("customer_code");

        // 如果是总行或分行，查找所有子节点
        if ("0".equals(branchType) || "1".equals(branchType)) {
            Map<String, Object> childParams = new HashMap<>();
            childParams.put("parentId", customerCode);
            List<Map<String, Object>> childCustomers = sqlSession.selectList("customer.findChildCustomers",
                    childParams);

            // 递归构建子节点树
            for (Map<String, Object> child : childCustomers) {
                Map<String, Object> childNode = buildNodeTree(child);
                children.add(childNode);
            }
        }

        if (!children.isEmpty()) {
            node.put("children", children);
        }

        return node;
    }

    /**
     * 递归构建简化的节点树
     *
     * @param customer 当前客户
     * @return 简化的节点树
     */
    private Map<String, Object> buildSimpleNodeTree(Map<String, Object> customer) {
        Map<String, Object> node = new HashMap<>();

        // 只保留必要的字段（按照 SimpleCustomerInfoDto 的字段）
        // 注意：数据库字段名使用下划线，需要正确映射
        node.put("customerId", customer.get("customer_id"));
        node.put("customerCode", customer.get("customer_code"));
        node.put("customerName", customer.get("customer_name"));
        node.put("branchType", customer.get("branch_type"));
        node.put("type", customer.get("type"));
        node.put("area", customer.get("area"));
        node.put("deliveryMethod", customer.get("delivery_method"));

        List<Map<String, Object>> children = new ArrayList<>();

        String branchType = (String) customer.get("branch_type");
        String customerCode = (String) customer.get("customer_code");

        // 如果是总行或分行，查找所有子节点
        if ("0".equals(branchType) || "1".equals(branchType)) {
            Map<String, Object> childParams = new HashMap<>();
            childParams.put("parentId", customerCode);
            List<Map<String, Object>> childCustomers = sqlSession.selectList("customer.findChildCustomers",
                    childParams);

            // 递归构建子节点树
            for (Map<String, Object> child : childCustomers) {
                Map<String, Object> childNode = buildSimpleNodeTree(child);
                children.add(childNode);
            }
        }

        if (!children.isEmpty()) {
            node.put("children", children);
        }

        return node;
    }

    /**
     * 找出根节点客户（没有父级在查询结果中的客户）
     *
     * @param customers 所有客户列表
     * @return 根节点客户列表
     */
    private List<Map<String, Object>> findRootCustomers(List<Map<String, Object>> customers) {
        // 创建客户编码集合，用于快速查找
        Set<String> customerCodeSet = customers.stream()
                .map(c -> (String) c.get("customer_code"))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 找出没有父级在查询结果中的客户
        return customers.stream()
                .filter(customer -> {
                    String parentId = (String) customer.get("parent_id");
                    // 如果没有父级ID，则是根节点
                    if (parentId == null) {
                        return true;
                    }
                    // 如果父级ID不在查询结果中，则也是根节点
                    return !customerCodeSet.contains(parentId);
                })
                .collect(Collectors.toList());
    }
}