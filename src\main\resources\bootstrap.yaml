server:
  port: 8006
spring:
  application:
    name: sqlexecutor-rest
  profiles:
    active: ${PROFILES_ACTIVE:sit}
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务
  cloud:
    service-registry:
      auto-registration:
        enabled: ${REGIST_ENABLE:true}
    nacos:
      server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yaml
        namespace: ${spring.profiles.active}
        shared-configs[0]:
          data-id: env.yaml
          refresh: true
      discovery:
        namespace: ${spring.profiles.active}
