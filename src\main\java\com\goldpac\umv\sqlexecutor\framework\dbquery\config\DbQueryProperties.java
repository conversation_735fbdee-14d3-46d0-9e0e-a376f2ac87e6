package com.goldpac.umv.sqlexecutor.framework.dbquery.config;

import com.goldpac.umv.sqlexecutor.framework.dbquery.core.enums.QueryResultTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @created at 2025/7/8 星期二
 */
@Data
@ConfigurationProperties(prefix = "sql-executor.db-query")
public class DbQueryProperties {

    private List<String> dbQueryUrlPatterns = Arrays.asList("/db-query/**");
    private Collection<QueryRequest> queryRequests = Collections.emptyList();

    @Data
    public static class QueryRequest {
        private String method;
        private String urlPattern;
        private String statementId;
        private QueryResultTypeEnum resultType;
    }

}
