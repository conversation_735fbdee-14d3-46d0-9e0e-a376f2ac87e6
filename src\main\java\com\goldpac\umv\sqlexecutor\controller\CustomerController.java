package com.goldpac.umv.sqlexecutor.controller;

import com.goldpac.umv.framework.pojo.CommonResult;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.annotation.BypassDbQueryFilter;
import com.goldpac.umv.sqlexecutor.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户管理控制器
 * 提供客户信息查询相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/db-query/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    /**
     * 查询客户信息
     *
     * @param request        HTTP请求
     * @param customerId     客户ID
     * @param customerName   客户名称
     * @param customerCode   客户编码
     * @param uscc           统一社会信用代码
     * @param deliveryMethod 交付方式（逗号分隔）
     * @return 客户信息列表
     */
    @BypassDbQueryFilter
    @GetMapping("/getCustomerList")
    public CommonResult<Object> getCustomerList(
            HttpServletRequest request,
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String customerName,
            @RequestParam(required = false) String customerCode,
            @RequestParam(required = false) String uscc,
            @RequestParam(required = false) String deliveryMethod) {

        try {
            Map<String, Object> params = new HashMap<>();

            // 添加查询参数
            if (customerId != null) {
                params.put("customerId", customerId);
            }
            if (customerName != null) {
                params.put("customerName", customerName);
            }
            if (customerCode != null) {
                params.put("customerCode", customerCode);
            }
            if (uscc != null) {
                params.put("uscc", uscc);
            }
            if (deliveryMethod != null) {
                params.put("deliveryMethod", deliveryMethod);
            }

            return customerService.findCustomers(params);

        } catch (Exception e) {
            log.error("查询客户信息时发生错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 计算总行和分行数量
     *
     * @param request     HTTP请求
     * @param companyType 公司类型（交付方式，逗号分隔）
     * @return 统计结果
     */
    @BypassDbQueryFilter
    @GetMapping("/count")
    public CommonResult<Object> countCustomers(
            HttpServletRequest request,
            @RequestParam(required = false) String companyType) {

        try {
            Map<String, Object> params = new HashMap<>();

            if (companyType != null) {
                params.put("companyType", companyType);
            }

            return customerService.countCustomersByType(params);

        } catch (Exception e) {
            log.error("统计客户数量时发生错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取客户节点树
     *
     * @param request      HTTP请求
     * @param customerId   客户ID
     * @param customerCode 客户编码
     * @return 客户节点树
     */
    @BypassDbQueryFilter
    @GetMapping("/node-tree")
    public CommonResult<Object> getCustomerNodeTree(
            HttpServletRequest request,
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String customerCode) {

        try {
            Map<String, Object> params = new HashMap<>();

            if (customerId != null) {
                params.put("customerId", customerId);
            }
            if (customerCode != null) {
                params.put("customerCode", customerCode);
            }

            return customerService.getCustomerNodeTree(params);
        } catch (Exception e) {
            log.error("获取客户节点树时发生错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取客户节点树列表
     *
     * @param customerId     客户ID
     * @param customerName   客户名称
     * @param customerIds    客户ID列表（逗号分隔）
     * @param customerCodes  客户编码列表（逗号分隔）
     * @param branchType     分行类型
     * @param deliveryMethod 交付方式（逗号分隔）
     * @return 客户节点树列表
     */
    @BypassDbQueryFilter
    @GetMapping("/node-tree-list")
    public CommonResult<Object> getCustomerNodeTreeList(
            HttpServletRequest request,
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String customerName,
            @RequestParam(required = false) String customerIds,
            @RequestParam(required = false) String customerCodes,
            @RequestParam(required = false) String branchType,
            @RequestParam(required = false) String deliveryMethod) {

        try {
            Map<String, Object> params = new HashMap<>();

            // 添加查询参数
            if (customerId != null) {
                params.put("customerId", customerId);
            }
            if (customerName != null) {
                params.put("customerName", customerName);
            }
            if (customerIds != null) {
                params.put("customerIds", customerIds);
            }
            if (customerCodes != null) {
                params.put("customerCodes", customerCodes);
            }
            if (branchType != null) {
                params.put("branchType", branchType);
            }
            if (deliveryMethod != null) {
                params.put("deliveryMethod", deliveryMethod);
            }

            return customerService.getCustomerNodeTreeList(params);

        } catch (Exception e) {
            log.error("获取客户节点树列表时发生错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取客户节点树列表（简化信息，树形结构，避免重复，支持分页）
     *
     * @param customerId     客户ID
     * @param customerName   客户名称
     * @param customerIds    客户ID列表（逗号分隔）
     * @param customerCodes  客户编码列表（逗号分隔）
     * @param branchType     分行类型
     * @param deliveryMethod 交付方式（逗号分隔）
     * @param page           页码
     * @param pageSize       每页条数
     * @return 分页的客户节点树列表
     */
    @BypassDbQueryFilter
    @GetMapping("/simple-node-tree-list")
    public CommonResult<Object> getSimpleCustomerNodeTreeList(
            HttpServletRequest request,
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String customerName,
            @RequestParam(required = false) String customerIds,
            @RequestParam(required = false) String customerCodes,
            @RequestParam(required = false) String branchType,
            @RequestParam(required = false) String deliveryMethod,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize) {

        try {
            Map<String, Object> params = new HashMap<>();

            // 添加查询参数
            if (customerId != null) {
                params.put("customerId", customerId);
            }
            if (customerName != null) {
                params.put("customerName", customerName);
            }
            if (customerIds != null) {
                params.put("customerIds", customerIds);
            }
            if (customerCodes != null) {
                params.put("customerCodes", customerCodes);
            }
            if (branchType != null) {
                params.put("branchType", branchType);
            }
            if (deliveryMethod != null) {
                params.put("deliveryMethod", deliveryMethod);
            }

            // 添加分页参数
            params.put("page", page);
            params.put("pageSize", pageSize);

            return customerService.getSimpleCustomerNodeTreeList(params);

        } catch (Exception e) {
            log.error("获取简化客户节点树列表时发生错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "处理请求时发生错误: " + e.getMessage());
        }
    }
}
