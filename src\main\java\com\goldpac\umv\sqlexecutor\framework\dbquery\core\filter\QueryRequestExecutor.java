package com.goldpac.umv.sqlexecutor.framework.dbquery.core.filter;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.goldpac.umv.framework.pojo.CommonResult;
import com.goldpac.umv.sqlexecutor.framework.dbquery.config.DbQueryProperties;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.enums.QueryResultTypeEnum;
import com.goldpac.umv.sqlexecutor.framework.dbquery.core.executor.MybatisStatementExecutor;
import com.goldpac.umv.sqlexecutor.framework.dbquery.util.PaginationUtil;
import com.goldpac.umv.sqlexecutor.utils.RequestUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.http.MediaType;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.goldpac.umv.framework.exception.util.ServiceExceptionUtil.exception;
import static com.goldpac.umv.sqlexecutor.ErrorCodeConstants.QUERY_REQUEST_NOT_MATCH_ERROR;

/**
 * <AUTHOR>
 * @created at 2025/7/8 星期二
 */
@AllArgsConstructor
public class QueryRequestExecutor {

    private static AntPathMatcher antPathMatcher = new AntPathMatcher();

    private DbQueryProperties properties;

    private MybatisStatementExecutor statementExecutor;

    public CommonResult<Object> execute(HttpServletRequest request) {
        QueryRequestMatchOutcome matchOutcome = obtainMatchQueryRequest(request);
        if (!matchOutcome.isSuccess()) {
            throw exception(QUERY_REQUEST_NOT_MATCH_ERROR, request.getRequestURI());
        }
        DbQueryProperties.QueryRequest queryRequest = matchOutcome.getQueryRequest();

        // 处理分页参数
        Map<String, Object> processedParams = PaginationUtil.processPaginationParams(matchOutcome.getParameters());

        Object selectResult;
        if (QueryResultTypeEnum.ONE == queryRequest.getResultType()) {
            selectResult = statementExecutor.executeSelectOne(queryRequest.getStatementId(),
                    processedParams);
        } else {
            selectResult = statementExecutor.executeSelect(
                    matchOutcome.getQueryRequest().getStatementId(), processedParams);
        }
        return CommonResult.success(selectResult);
    }

    private QueryRequestMatchOutcome obtainMatchQueryRequest(HttpServletRequest request) {
        String url = RequestUtils.getRequestPath(request);
        String method = request.getMethod();
        Collection<DbQueryProperties.QueryRequest> queryRequests = properties.getQueryRequests();
        for (DbQueryProperties.QueryRequest queryRequest : queryRequests) {
            if (StringUtils.hasText(queryRequest.getMethod()) && !queryRequest.getMethod().equalsIgnoreCase(method)) {
                continue;
            }
            if (!antPathMatcher.match(queryRequest.getUrlPattern(), url)) {
                continue;
            }
            Map<String, Object> parameters = extractParameters(url, request, queryRequest);
            return QueryRequestMatchOutcome.match(queryRequest, parameters);
        }
        return QueryRequestMatchOutcome.notMatch();
    }

    private Map<String, Object> extractParameters(String url, HttpServletRequest servletRequest,
            DbQueryProperties.QueryRequest queryRequest) {
        Map<String, Object> parameters = new LinkedHashMap<>();
        if (!StringUtils.hasText(url)) {
            url = RequestUtils.getRequestPath(servletRequest);
        }
        Map<String, String> uriTemplateVariables = antPathMatcher
                .extractUriTemplateVariables(queryRequest.getUrlPattern(), url);
        if (MapUtil.isNotEmpty(uriTemplateVariables)) {
            parameters.putAll(uriTemplateVariables);
        }
        servletRequest.getParameterMap().forEach((k, v) -> {
            if (ArrayUtil.isEmpty(v)) {
                parameters.put(k, null);
            } else if (v.length == 1) {
                parameters.put(k, v[0]);
            } else {
                parameters.put(k, Arrays.asList(v));
            }
        });
        String contentType = servletRequest.getContentType();
        if (StringUtils.hasText(contentType)
                && MediaType.APPLICATION_JSON.isCompatibleWith(MediaType.valueOf(contentType))) {
            String requestBody = ServletUtil.getBody(servletRequest);
            Map<String, Object> requestBodyMap = JsonUtils.parseObject(requestBody,
                    new TypeReference<Map<String, Object>>() {
                    });
            if (MapUtil.isNotEmpty(requestBodyMap)) {
                parameters.putAll(requestBodyMap);
            }
        }
        return parameters;
    }

    @Data
    public static class QueryRequestMatchOutcome {
        private boolean success;
        private DbQueryProperties.QueryRequest queryRequest;
        private Map<String, Object> parameters;

        public static QueryRequestMatchOutcome match(DbQueryProperties.QueryRequest queryRequest,
                Map<String, Object> parameters) {
            return new QueryRequestMatchOutcome().setSuccess(true).setQueryRequest(queryRequest)
                    .setParameters(parameters);
        }

        public static QueryRequestMatchOutcome notMatch() {
            return new QueryRequestMatchOutcome().setSuccess(false);
        }
    }

}
