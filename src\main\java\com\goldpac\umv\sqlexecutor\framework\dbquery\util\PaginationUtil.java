package com.goldpac.umv.sqlexecutor.framework.dbquery.util;

import java.util.Map;

/**
 * 分页工具类
 * 用于处理分页参数，避免在 MyBatis 中进行复杂计算
 */
public class PaginationUtil {
    
    /**
     * 处理分页参数，计算 offset 和 limit
     * 
     * @param params 请求参数 Map
     * @return 处理后的参数 Map（包含 offset 和 limit）
     */
    public static Map<String, Object> processPaginationParams(Map<String, Object> params) {
        if (params == null) {
            return params;
        }
        
        try {
            Object pageNumObj = params.get("pageNum");
            Object pageSizeObj = params.get("pageSize");
            
            if (pageNumObj != null && pageSizeObj != null) {
                int pageNum = parseInteger(pageNumObj, 1);
                int pageSize = parseInteger(pageSizeObj, 20);
                
                // 计算偏移量
                int offset = (pageNum - 1) * pageSize;
                
                // 添加计算后的参数
                params.put("offset", offset);
                params.put("limit", pageSize);
                
                // 保留原始参数
                params.put("pageNumInt", pageNum);
                params.put("pageSizeInt", pageSize);
            } else {
                // 默认分页
                params.put("offset", 0);
                params.put("limit", 20);
                params.put("pageNumInt", 1);
                params.put("pageSizeInt", 20);
            }
        } catch (Exception e) {
            // 异常情况使用默认分页
            params.put("offset", 0);
            params.put("limit", 20);
            params.put("pageNumInt", 1);
            params.put("pageSizeInt", 20);
        }
        
        return params;
    }
    
    /**
     * 安全地解析整数
     * 
     * @param obj 要解析的对象
     * @param defaultValue 默认值
     * @return 解析后的整数
     */
    private static int parseInteger(Object obj, int defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        try {
            if (obj instanceof Integer) {
                return (Integer) obj;
            } else if (obj instanceof String) {
                String str = (String) obj;
                if (str.trim().isEmpty()) {
                    return defaultValue;
                }
                return Integer.parseInt(str.trim());
            } else {
                return Integer.parseInt(obj.toString().trim());
            }
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 验证分页参数是否有效
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 是否有效
     */
    public static boolean isValidPagination(Object pageNum, Object pageSize) {
        try {
            int num = parseInteger(pageNum, -1);
            int size = parseInteger(pageSize, -1);
            return num > 0 && size > 0 && size <= 1000; // 限制最大页大小
        } catch (Exception e) {
            return false;
        }
    }
}
