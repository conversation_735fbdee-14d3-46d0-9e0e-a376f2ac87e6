package com.goldpac.umv.sqlexecutor;

import com.goldpac.umv.framework.exception.ErrorCode;

/**
 * 工作流 错误码枚举类
 *
 * 工作流系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {

    // ==========  通用流程处理 模块 1-009-000-000 ==========
    ErrorCode UN_SUPPORT_OPERATION_ERROR = new ErrorCode(1001000002, "暂不支持该操作");
    ErrorCode QUERY_REQUEST_NOT_MATCH_ERROR = new ErrorCode(1001000003, "找不到与({})相匹配的statementId，请检查。");



}
