package com.goldpac.umv.sqlexecutor.controller;

import com.goldpac.umv.sqlexecutor.framework.dbquery.core.annotation.BypassDbQueryFilter;
import com.goldpac.umv.sqlexecutor.service.CustomerAuditService;
import com.goldpac.umv.framework.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户审计控制器
 * 提供客户审计记录相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/db-query/manifest")
public class CustomerAuditController {

    @Autowired
    private CustomerAuditService customerAuditService;

    /**
     * 获取客户审计记录变更
     * 
     * @param request HTTP请求
     * @param params  请求参数
     * @return 变更记录
     */
    @BypassDbQueryFilter
    @PostMapping("/getCustomerAuditRecordChanges")
    public CommonResult<Object> getCustomerAuditRecordChanges(
            HttpServletRequest request,
            @RequestBody(required = false) Map<String, Object> params) {

        try {
            // 合并请求参数
            Map<String, Object> allParams = new HashMap<>();

            // 添加URL参数
            if (request.getParameterMap() != null) {
                request.getParameterMap().forEach((key, values) -> {
                    if (values != null && values.length > 0) {
                        allParams.put(key, values[0]);
                    }
                });
            }

            // 添加请求体参数
            if (params != null) {
                allParams.putAll(params);
            }

            return customerAuditService.getCustomerAuditRecordChanges(allParams);

        } catch (Exception e) {
            log.error("处理请求时发生错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "处理请求时发生错误: " + e.getMessage());
        }
    }
}
