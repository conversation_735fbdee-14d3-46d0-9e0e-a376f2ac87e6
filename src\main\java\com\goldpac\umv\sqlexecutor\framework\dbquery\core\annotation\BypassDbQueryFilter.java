package com.goldpac.umv.sqlexecutor.framework.dbquery.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于绕过 DbQueryFilter 的注解。
 * 当应用于 Controller 的方法或类时，它将阻止 DbQueryFilter 处理该请求。
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface BypassDbQueryFilter {
}
