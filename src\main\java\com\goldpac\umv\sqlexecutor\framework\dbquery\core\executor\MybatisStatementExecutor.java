package com.goldpac.umv.sqlexecutor.framework.dbquery.core.executor;

import java.util.List;
import java.util.Map;

import static com.goldpac.umv.framework.exception.util.ServiceExceptionUtil.exception;
import static com.goldpac.umv.sqlexecutor.ErrorCodeConstants.UN_SUPPORT_OPERATION_ERROR;

/**
 * <AUTHOR>
 * @created at 2025/7/8 星期二
 */
public interface MybatisStatementExecutor {

    /**
     * 执行selectOne。
     *
     * @param statementId
     * @param params
     * @return
     */
    Map<String, Object> executeSelectOne(String statementId, Map<String, Object> params);

    /**
     * 执行select。
     *
     * @param statementId
     * @param params
     * @return
     */
    List<Map<String, Object>> executeSelect(String statementId, Map<String, Object> params);

    /**
     * 执行update。
     *
     * @param statementId
     * @param params
     * @return
     */
    default int executeUpdate(String statementId, Map<String, Object> params) {
        throw exception(UN_SUPPORT_OPERATION_ERROR);
    }

    /**
     * 执行delete。
     *
     * @param statementId
     * @param params
     * @return
     */
    default int executeDelete(String statementId, Map<String, Object> params) {
        throw exception(UN_SUPPORT_OPERATION_ERROR);
    }

    /**
     * 执行insert。
     *
     * @param statementId
     * @param params
     * @return
     */
    default int executeInsert(String statementId, Map<String, Object> params) {
        throw exception(UN_SUPPORT_OPERATION_ERROR);
    }

}
