<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="customer">

    <!-- 根据查询条件查询客户信息 -->
    <select id="findCustomers" resultType="java.util.Map">
        SELECT 
            customer_id,
            customer_code,
            customer_name,
            uscc,
            branch_type,
            delivery_method,
            parent_id,
            create_time,
            update_time
        FROM customer_info 
        WHERE 1 = 1
        <if test="customerId != null">
            AND customer_id = #{customerId}
        </if>
        <if test="customerName != null">
            AND customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="customerCode != null">
            AND customer_code = #{customerCode}
        </if>
        <if test="uscc != null">
            AND uscc = #{uscc}
        </if>
        <if test="deliveryMethod != null">
            AND delivery_method IN
            <foreach collection="deliveryMethod" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND (cdc_op IS NULL OR cdc_op != 'd')
        ORDER BY customer_name ASC
    </select>

    <!-- 根据客户类型统计客户数量 -->
    <select id="countCustomersByType" resultType="java.util.Map">
        SELECT COUNT(1) AS count 
        FROM customer_info 
        WHERE 1 = 1
        <if test="branchType != null">
            AND branch_type = #{branchType}
        </if>
        <if test="companyType != null">
            AND delivery_method IN
            <foreach collection="companyType" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND (cdc_op IS NULL OR cdc_op != 'd')
    </select>

    <!-- 根据客户ID查找客户 -->
    <select id="findCustomerById" resultType="java.util.Map">
        SELECT 
            customer_id,
            customer_code,
            customer_name,
            uscc,
            branch_type,
            delivery_method,
            parent_id,
            create_time,
            update_time
        FROM customer_info 
        WHERE customer_id = #{customerId}
        AND (cdc_op IS NULL OR cdc_op != 'd')
    </select>

    <!-- 根据客户编码查找客户 -->
    <select id="findCustomerByCode" resultType="java.util.Map">
        SELECT 
            customer_id,
            customer_code,
            customer_name,
            uscc,
            branch_type,
            delivery_method,
            parent_id,
            create_time,
            update_time
        FROM customer_info 
        WHERE customer_code = #{customerCode}
        AND (cdc_op IS NULL OR cdc_op != 'd')
    </select>

    <!-- 查找客户用于构建节点树 -->
    <select id="findCustomersForNodeTree" resultType="java.util.Map">
        SELECT 
            customer_id,
            customer_code,
            customer_name,
            uscc,
            branch_type,
            delivery_method,
            parent_id,
            create_time,
            update_time
        FROM customer_info 
        WHERE 1 = 1
        <if test="customerId != null">
            AND customer_id = #{customerId}
        </if>
        <if test="customerIds != null">
            AND customer_id IN
            <foreach collection="customerIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerCodes != null">
            AND customer_code IN
            <foreach collection="customerCodes" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="branchType != null">
            AND branch_type = #{branchType}
        </if>
        <if test="deliveryMethod != null">
            AND delivery_method IN
            <foreach collection="deliveryMethod" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerName != null">
            AND customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        AND (cdc_op IS NULL OR cdc_op != 'd')
        ORDER BY customer_name ASC
    </select>

    <!-- 查找子客户 -->
    <select id="findChildCustomers" resultType="java.util.Map">
        SELECT 
            customer_id,
            customer_code,
            customer_name,
            uscc,
            branch_type,
            delivery_method,
            parent_id,
            create_time,
            update_time
        FROM customer_info 
        WHERE parent_id = #{parentId}
        AND (cdc_op IS NULL OR cdc_op != 'd')
        ORDER BY customer_name ASC
    </select>

</mapper>
